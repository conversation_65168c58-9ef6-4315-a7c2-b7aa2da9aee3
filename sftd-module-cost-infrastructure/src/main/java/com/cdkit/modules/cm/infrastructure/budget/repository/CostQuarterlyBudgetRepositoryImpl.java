package com.cdkit.modules.cm.infrastructure.budget.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;
import com.cdkit.modules.cm.domain.budget.repository.CostQuarterlyBudgetRepository;
import com.cdkit.modules.cm.infrastructure.budget.converter.CostQuarterlyBudgetConverter;
import com.cdkit.modules.cm.infrastructure.budget.entity.CostQuarterlyBudget;
import com.cdkit.modules.cm.infrastructure.budget.mapper.CostQuarterlyBudgetMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 季度预算仓储实现
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
@Repository
@RequiredArgsConstructor
@Slf4j
public class CostQuarterlyBudgetRepositoryImpl implements CostQuarterlyBudgetRepository {

    private final CostQuarterlyBudgetMapper costQuarterlyBudgetMapper;
    private final CostQuarterlyBudgetConverter costQuarterlyBudgetConverter;

    @Override
    public CostQuarterlyBudgetEntity save(CostQuarterlyBudgetEntity entity) {
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetConverter.toInfrastructure(entity);
        costQuarterlyBudgetMapper.insert(infraEntity);
        return costQuarterlyBudgetConverter.toDomain(infraEntity);
    }

    @Override
    public CostQuarterlyBudgetEntity updateById(CostQuarterlyBudgetEntity entity) {
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetConverter.toInfrastructure(entity);
        costQuarterlyBudgetMapper.updateById(infraEntity);
        return costQuarterlyBudgetConverter.toDomain(infraEntity);
    }

    @Override
    public void deleteById(String id) {
        costQuarterlyBudgetMapper.deleteById(id);
    }

    @Override
    public void deleteByIds(List<String> ids) {
        costQuarterlyBudgetMapper.deleteBatchIds(ids);
    }

    @Override
    public CostQuarterlyBudgetEntity findById(String id) {
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetMapper.selectById(id);
        return infraEntity != null ? costQuarterlyBudgetConverter.toDomain(infraEntity) : null;
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByIds(List<String> ids) {
        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectBatchIds(ids);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public PageRes<CostQuarterlyBudgetEntity> queryPageList(CostQuarterlyBudgetEntity queryEntity, PageReq pageReq) {
        Page<CostQuarterlyBudget> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());
        
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        
        // 构建查询条件
        if (queryEntity != null) {
            if (StringUtils.hasText(queryEntity.getQuarterlyBudgetNo())) {
                queryWrapper.like(CostQuarterlyBudget::getQuarterlyBudgetNo, queryEntity.getQuarterlyBudgetNo());
            }
            if (StringUtils.hasText(queryEntity.getQuarterlyBudgetName())) {
                queryWrapper.like(CostQuarterlyBudget::getQuarterlyBudgetName, queryEntity.getQuarterlyBudgetName());
            }
            if (StringUtils.hasText(queryEntity.getBudgetStatus())) {
                queryWrapper.eq(CostQuarterlyBudget::getBudgetStatus, queryEntity.getBudgetStatus());
            }
            if (StringUtils.hasText(queryEntity.getProfessionalCompany())) {
                queryWrapper.like(CostQuarterlyBudget::getProfessionalCompany, queryEntity.getProfessionalCompany());
            }
            if (StringUtils.hasText(queryEntity.getQuarter())) {
                queryWrapper.eq(CostQuarterlyBudget::getQuarter, queryEntity.getQuarter());
            }
            if (StringUtils.hasText(queryEntity.getAnnualBudgetCode())) {
                queryWrapper.eq(CostQuarterlyBudget::getAnnualBudgetCode, queryEntity.getAnnualBudgetCode());
            }
        }
        
        // 添加排序
        if (pageReq.getOrderParam() != null && !pageReq.getOrderParam().isEmpty()) {
            pageReq.getOrderParam().forEach(orderParam -> {
                if ("desc".equalsIgnoreCase(orderParam.getOrder())) {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByDesc(CostQuarterlyBudget::getCreateTime);
                    }
                } else {
                    if ("create_time".equals(orderParam.getField())) {
                        queryWrapper.orderByAsc(CostQuarterlyBudget::getCreateTime);
                    }
                }
            });
        } else {
            // 默认按创建时间倒序
            queryWrapper.orderByDesc(CostQuarterlyBudget::getCreateTime);
        }

        IPage<CostQuarterlyBudget> pageResult = costQuarterlyBudgetMapper.selectPage(page, queryWrapper);
        
        List<CostQuarterlyBudgetEntity> domainEntities = pageResult.getRecords().stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());

        return PageRes.of(pageResult.getCurrent(), pageResult.getSize(), domainEntities, pageResult.getTotal(), pageResult.getPages());
    }

    @Override
    public CostQuarterlyBudgetEntity findByQuarterlyBudgetNo(String quarterlyBudgetNo) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getQuarterlyBudgetNo, quarterlyBudgetNo);
        
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetMapper.selectOne(queryWrapper);
        return infraEntity != null ? costQuarterlyBudgetConverter.toDomain(infraEntity) : null;
    }

    @Override
    public CostQuarterlyBudgetEntity findByQuarterlyBudgetCode(String quarterlyBudgetCode) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getQuarterlyBudgetCode, quarterlyBudgetCode);
        
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetMapper.selectOne(queryWrapper);
        return infraEntity != null ? costQuarterlyBudgetConverter.toDomain(infraEntity) : null;
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByBudgetStatus(String budgetStatus) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getBudgetStatus, budgetStatus);
        
        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public String findMaxQuarterlyBudgetNoByDate(String date) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        // 查询以"JDYS" + date开头的预算编号
        String prefix = "JDYS" + date;
        queryWrapper.likeRight(CostQuarterlyBudget::getQuarterlyBudgetNo, prefix);
        queryWrapper.orderByDesc(CostQuarterlyBudget::getQuarterlyBudgetNo);
        queryWrapper.last("LIMIT 1");
        
        CostQuarterlyBudget infraEntity = costQuarterlyBudgetMapper.selectOne(queryWrapper);
        return infraEntity != null ? infraEntity.getQuarterlyBudgetNo() : null;
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByAnnualBudgetId(String annualBudgetId) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getAnnualBudgetCode, annualBudgetId);

        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByQuarterlyPlanId(String quarterlyPlanId) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getQuarterlyPlanNo, quarterlyPlanId);

        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByAnnualBudgetIdAndStatus(String annualBudgetCode, String budgetStatus) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getAnnualBudgetCode, annualBudgetCode);
        queryWrapper.eq(CostQuarterlyBudget::getBudgetStatus, budgetStatus);

        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByProfessionalCompany(String professionalCompany) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getProfessionalCompany, professionalCompany);
        
        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }

    @Override
    public List<CostQuarterlyBudgetEntity> findByQuarter(String quarter) {
        LambdaQueryWrapper<CostQuarterlyBudget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostQuarterlyBudget::getQuarter, quarter);
        
        List<CostQuarterlyBudget> infraEntities = costQuarterlyBudgetMapper.selectList(queryWrapper);
        return infraEntities.stream()
                .map(costQuarterlyBudgetConverter::toDomain)
                .collect(Collectors.toList());
    }



    @Override
    public void updateBudgetStatusBatch(List<String> budgetIds, String newStatus) {
        LambdaUpdateWrapper<CostQuarterlyBudget> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(CostQuarterlyBudget::getId, budgetIds);
        updateWrapper.set(CostQuarterlyBudget::getBudgetStatus, newStatus);

        costQuarterlyBudgetMapper.update(null, updateWrapper);
        log.info("批量更新季度预算状态完成，更新数量: {}, 新状态: {}", budgetIds.size(), newStatus);
    }

    @Override
    public java.math.BigDecimal sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(String annualBudgetId, String excludeQuarterlyBudgetId) {
        return costQuarterlyBudgetMapper.sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(annualBudgetId, excludeQuarterlyBudgetId);
    }

    @Override
    public java.math.BigDecimal sumApprovedQuarterlyBudgetExpenditureBySubjectCode(String annualBudgetId, String budgetSubjectCode, String excludeQuarterlyBudgetId) {
        return costQuarterlyBudgetMapper.sumApprovedQuarterlyBudgetExpenditureBySubjectCode(annualBudgetId, budgetSubjectCode, excludeQuarterlyBudgetId);
    }
}
