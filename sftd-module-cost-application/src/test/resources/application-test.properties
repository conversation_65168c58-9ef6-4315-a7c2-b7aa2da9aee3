# ????????

# ????? - ??H2?????????
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE;MODE=MySQL
spring.datasource.username=sa
spring.datasource.password=

# H2??????
spring.sql.init.mode=always
spring.sql.init.schema-locations=classpath:schema-test.sql
spring.sql.init.data-locations=classpath:data-test.sql

# JPA??
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.format_sql=true
spring.jpa.database-platform=org.hibernate.dialect.H2Dialect

# MyBatis??
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl
mybatis-plus.global-config.db-config.logic-delete-field=delFlag
mybatis-plus.global-config.db-config.logic-delete-value=1
mybatis-plus.global-config.db-config.logic-not-delete-value=0

# ????
logging.level.com.cdkit.modules.cm=DEBUG
logging.level.org.springframework.context.event=DEBUG
logging.level.org.springframework.transaction=DEBUG
logging.level.org.springframework.jdbc=DEBUG

# ????
spring.task.execution.pool.core-size=2
spring.task.execution.pool.max-size=5
spring.task.execution.pool.queue-capacity=50
spring.task.execution.pool.keep-alive=30s
spring.task.execution.thread-name-prefix=test-async-

# ????
spring.transaction.default-timeout=30

# ??????
test.cleanup.enabled=true
test.data.tenant-id=1
test.data.operator=test-user
