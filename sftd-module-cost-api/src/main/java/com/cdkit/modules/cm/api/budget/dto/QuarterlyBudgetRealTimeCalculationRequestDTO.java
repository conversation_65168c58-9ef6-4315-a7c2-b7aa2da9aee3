package com.cdkit.modules.cm.api.budget.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 季度预算实时计算请求DTO
 * 
 * <AUTHOR>
 * @date 2025-08-19
 */
@Schema(description = "季度预算实时计算请求DTO")
@Data
public class QuarterlyBudgetRealTimeCalculationRequestDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**季度计划ID（必填）*/
    @Schema(description = "季度计划ID（必填）")
    private String quarterlyPlanId;

    /**年度预算ID（必填）*/
    @Schema(description = "项目年度预算ID（必填）")
    private String annualBudgetId;

    /**项目收入预算总额（不含税，元）*/
    @Schema(description = "项目收入预算总额（不含税，元）")
    private BigDecimal projectRevenueBudgetTotal;

    /**采办包明细变更数据*/
    @Schema(description = "采办包明细变更数据")
    private List<ProcurementPackageDetailDTO> procurementPackageDetails;

    /**
     * 采办包明细DTO
     */
    @Schema(description = "采办包明细DTO")
    @Data
    public static class ProcurementPackageDetailDTO implements Serializable {
        private static final long serialVersionUID = 1L;

        /**预算科目编码（关联预算科目表）*/
        @Schema(description = "预算科目编码（关联预算科目表）")
        private String budgetSubjectCode;

        /**预算科目名称*/
        @Schema(description = "预算科目名称")
        private String budgetSubjectName;

        /**金额（元，必填，支持小数）*/
        @Schema(description = "金额（元，必填，支持小数）")
        private BigDecimal amount;
    }
}
