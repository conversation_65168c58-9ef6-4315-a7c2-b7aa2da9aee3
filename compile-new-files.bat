@echo off
echo Compiling new DDD event-driven architecture files...

echo.
echo 1. Compiling domain module...
call mvn clean compile -q -pl sftd-module-cost-domain
if %errorlevel% neq 0 (
    echo ERROR: Domain module compilation failed
    exit /b 1
)
echo Domain module compiled successfully

echo.
echo 2. Installing domain module to local repository...
call mvn install -q -pl sftd-module-cost-domain -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Domain module installation failed
    exit /b 1
)
echo Domain module installed successfully

echo.
echo 3. Compiling infrastructure module...
call mvn clean compile -q -pl sftd-module-cost-infrastructure
if %errorlevel% neq 0 (
    echo ERROR: Infrastructure module compilation failed
    exit /b 1
)
echo Infrastructure module compiled successfully

echo.
echo 4. Installing infrastructure module to local repository...
call mvn install -q -pl sftd-module-cost-infrastructure -DskipTests
if %errorlevel% neq 0 (
    echo ERROR: Infrastructure module installation failed
    exit /b 1
)
echo Infrastructure module installed successfully

echo.
echo ========================================
echo All new DDD files compiled successfully!
echo ========================================
echo.
echo Next steps:
echo 1. Execute the SQL DDL file: database/cost_procurement_execution_basis.sql
echo 2. Use QuarterlyBudgetApprovalApplication.approveBudgets() to trigger the event
echo 3. The system will automatically generate procurement execution basis
echo.

pause
