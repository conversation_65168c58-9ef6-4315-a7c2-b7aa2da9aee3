@echo off
echo 验证新增的DDD事件驱动代码...

echo.
echo 1. 检查领域事件类...
if exist "sftd-module-cost-domain\src\main\java\com\cdkit\modules\cm\domain\budget\event\QuarterlyBudgetLockedEvent.java" (
    echo ✓ QuarterlyBudgetLockedEvent.java 存在
) else (
    echo ✗ QuarterlyBudgetLockedEvent.java 不存在
)

echo.
echo 2. 检查领域服务接口...
if exist "sftd-module-cost-domain\src\main\java\com\cdkit\modules\cm\domain\procurement\service\ProcurementExecutionBasisGenerationService.java" (
    echo ✓ ProcurementExecutionBasisGenerationService.java 存在
) else (
    echo ✗ ProcurementExecutionBasisGenerationService.java 不存在
)

echo.
echo 3. 检查领域服务实现...
if exist "sftd-module-cost-domain\src\main\java\com\cdkit\modules\cm\domain\procurement\service\impl\ProcurementExecutionBasisGenerationServiceImpl.java" (
    echo ✓ ProcurementExecutionBasisGenerationServiceImpl.java 存在
) else (
    echo ✗ ProcurementExecutionBasisGenerationServiceImpl.java 不存在
)

echo.
echo 4. 检查状态更新服务...
if exist "sftd-module-cost-domain\src\main\java\com\cdkit\modules\cm\domain\budget\service\QuarterlyBudgetStatusUpdateService.java" (
    echo ✓ QuarterlyBudgetStatusUpdateService.java 存在
) else (
    echo ✗ QuarterlyBudgetStatusUpdateService.java 不存在
)

if exist "sftd-module-cost-domain\src\main\java\com\cdkit\modules\cm\domain\budget\service\impl\QuarterlyBudgetStatusUpdateServiceImpl.java" (
    echo ✓ QuarterlyBudgetStatusUpdateServiceImpl.java 存在
) else (
    echo ✗ QuarterlyBudgetStatusUpdateServiceImpl.java 不存在
)

echo.
echo 5. 检查事件监听器...
if exist "sftd-module-cost-application\src\main\java\com\cdkit\modules\cm\application\budget\event\ProcurementExecutionBasisEventListener.java" (
    echo ✓ ProcurementExecutionBasisEventListener.java 存在
) else (
    echo ✗ ProcurementExecutionBasisEventListener.java 不存在
)

echo.
echo 6. 检查应用服务...
if exist "sftd-module-cost-application\src\main\java\com\cdkit\modules\cm\application\budget\QuarterlyBudgetApprovalApplication.java" (
    echo ✓ QuarterlyBudgetApprovalApplication.java 存在
) else (
    echo ✗ QuarterlyBudgetApprovalApplication.java 不存在
)

echo.
echo 7. 检查异步配置...
if exist "sftd-module-cost-starter\src\main\java\com.cdkit\config\AsyncConfig.java" (
    echo ✓ AsyncConfig.java 存在
) else (
    echo ✗ AsyncConfig.java 不存在
)

echo.
echo 8. 检查测试文件...
if exist "sftd-module-cost-domain\src\test\java\com\cdkit\modules\cm\domain\procurement\service\ProcurementExecutionBasisGenerationServiceTest.java" (
    echo ✓ ProcurementExecutionBasisGenerationServiceTest.java 存在
) else (
    echo ✗ ProcurementExecutionBasisGenerationServiceTest.java 不存在
)

if exist "sftd-module-cost-application\src\test\java\com\cdkit\modules\cm\application\budget\event\ProcurementExecutionBasisEventListenerTest.java" (
    echo ✓ ProcurementExecutionBasisEventListenerTest.java 存在
) else (
    echo ✗ ProcurementExecutionBasisEventListenerTest.java 不存在
)

echo.
echo 9. 检查SQL DDL文件...
if exist "database\cost_procurement_execution_basis.sql" (
    echo ✓ cost_procurement_execution_basis.sql 存在
) else (
    echo ✗ cost_procurement_execution_basis.sql 不存在
)

echo.
echo ========================================
echo DDD事件驱动架构文件检查完成
echo ========================================
echo.
echo 新增文件列表：
echo - 领域事件：QuarterlyBudgetLockedEvent
echo - 领域服务：ProcurementExecutionBasisGenerationService + Impl
echo - 状态更新服务：QuarterlyBudgetStatusUpdateService + Impl  
echo - 事件监听器：ProcurementExecutionBasisEventListener
echo - 应用服务：QuarterlyBudgetApprovalApplication
echo - 异步配置：AsyncConfig
echo - 测试用例：2个测试类
echo - 数据库DDL：cost_procurement_execution_basis.sql
echo.
echo 总计：10个核心文件 + 2个测试文件 + 1个SQL文件
echo.

pause
