package com.cdkit.modules.cm.domain.procurement.service;

import com.cdkit.modules.cm.domain.procurement.service.impl.ProcurementExecutionBasisGenerationServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 采购执行依据生成服务测试
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@ExtendWith(MockitoExtension.class)
class ProcurementExecutionBasisGenerationServiceTest {

    @Mock
    private JdbcTemplate jdbcTemplate;

    @InjectMocks
    private ProcurementExecutionBasisGenerationServiceImpl procurementExecutionBasisGenerationService;

    private String testBudgetId;
    private Integer testTenantId;
    private String testOperatorBy;

    @BeforeEach
    void setUp() {
        testBudgetId = "test-budget-id-001";
        testTenantId = 1;
        testOperatorBy = "test-user";
    }

    @Test
    void testGenerateFromQuarterlyBudget_Success() {
        // Given
        when(jdbcTemplate.update(anyString(), any(), any(), any())).thenReturn(1);
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), any(), any())).thenReturn(2, 5, 3);

        // When
        ProcurementExecutionBasisGenerationService.GenerationResult result = 
            procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(testBudgetId, testTenantId, testOperatorBy);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(2, result.getMainRecordCount());
        assertEquals(5, result.getDetailRecordCount());
        assertEquals(3, result.getMaterialCount());
        assertNull(result.getErrorMessage());
        assertTrue(result.getProcessingTimeMs() >= 0);

        // Verify
        verify(jdbcTemplate).update("CALL sp_generate_procurement_execution_basis(?, ?, ?)", 
                testBudgetId, testTenantId, testOperatorBy);
        verify(jdbcTemplate, times(3)).queryForObject(anyString(), eq(Integer.class), any(), any());
    }

    @Test
    void testGenerateFromQuarterlyBudget_EmptyBudgetId() {
        // When
        ProcurementExecutionBasisGenerationService.GenerationResult result = 
            procurementExecutionBasisGenerationService.generateFromQuarterlyBudget("", testTenantId, testOperatorBy);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(0, result.getMainRecordCount());
        assertEquals(0, result.getDetailRecordCount());
        assertEquals(0, result.getMaterialCount());
        assertEquals("季度预算ID不能为空", result.getErrorMessage());

        // Verify
        verify(jdbcTemplate, never()).update(anyString(), any(), any(), any());
    }

    @Test
    void testGenerateFromQuarterlyBudget_NullTenantId() {
        // When
        ProcurementExecutionBasisGenerationService.GenerationResult result = 
            procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(testBudgetId, null, testOperatorBy);

        // Then
        assertFalse(result.isSuccess());
        assertEquals("租户ID不能为空", result.getErrorMessage());

        // Verify
        verify(jdbcTemplate, never()).update(anyString(), any(), any(), any());
    }

    @Test
    void testGenerateFromQuarterlyBudget_DatabaseException() {
        // Given
        when(jdbcTemplate.update(anyString(), any(), any(), any()))
            .thenThrow(new RuntimeException("Database connection failed"));

        // When
        ProcurementExecutionBasisGenerationService.GenerationResult result = 
            procurementExecutionBasisGenerationService.generateFromQuarterlyBudget(testBudgetId, testTenantId, testOperatorBy);

        // Then
        assertFalse(result.isSuccess());
        assertEquals(0, result.getMainRecordCount());
        assertTrue(result.getErrorMessage().contains("Database connection failed"));
        assertTrue(result.getProcessingTimeMs() >= 0);
    }

    @Test
    void testBatchGenerateFromQuarterlyBudgets_Success() {
        // Given
        List<String> budgetIds = Arrays.asList("budget-1", "budget-2", "budget-3");
        when(jdbcTemplate.update(anyString(), any(), any(), any())).thenReturn(1);
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), any(), any())).thenReturn(1, 2, 1);

        // When
        ProcurementExecutionBasisGenerationService.BatchGenerationResult result = 
            procurementExecutionBasisGenerationService.batchGenerateFromQuarterlyBudgets(budgetIds, testTenantId, testOperatorBy);

        // Then
        assertEquals(3, result.getTotalCount());
        assertEquals(3, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(3, result.getTotalMainRecordCount());
        assertEquals(6, result.getTotalDetailRecordCount());
        assertTrue(result.isAllSuccess());
        assertTrue(result.getFailureDetails().isEmpty());

        // Verify
        verify(jdbcTemplate, times(3)).update(anyString(), any(), any(), any());
    }

    @Test
    void testBatchGenerateFromQuarterlyBudgets_EmptyList() {
        // When
        ProcurementExecutionBasisGenerationService.BatchGenerationResult result = 
            procurementExecutionBasisGenerationService.batchGenerateFromQuarterlyBudgets(null, testTenantId, testOperatorBy);

        // Then
        assertEquals(0, result.getTotalCount());
        assertEquals(0, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertFalse(result.isAllSuccess());
        assertEquals(1, result.getFailureDetails().size());
        assertTrue(result.getFailureDetails().get(0).contains("季度预算ID列表不能为空"));
    }

    @Test
    void testBatchGenerateFromQuarterlyBudgets_PartialFailure() {
        // Given
        List<String> budgetIds = Arrays.asList("budget-1", "budget-2");
        when(jdbcTemplate.update(anyString(), eq("budget-1"), any(), any())).thenReturn(1);
        when(jdbcTemplate.update(anyString(), eq("budget-2"), any(), any()))
            .thenThrow(new RuntimeException("Database error"));
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), eq("budget-1"), any())).thenReturn(1, 2, 1);

        // When
        ProcurementExecutionBasisGenerationService.BatchGenerationResult result = 
            procurementExecutionBasisGenerationService.batchGenerateFromQuarterlyBudgets(budgetIds, testTenantId, testOperatorBy);

        // Then
        assertEquals(2, result.getTotalCount());
        assertEquals(1, result.getSuccessCount());
        assertEquals(1, result.getFailureCount());
        assertFalse(result.isAllSuccess());
        assertEquals(1, result.getFailureDetails().size());
        assertTrue(result.getFailureDetails().get(0).contains("budget-2"));
    }

    @Test
    void testRegenerateFromQuarterlyBudget_Success() {
        // Given
        when(jdbcTemplate.update(anyString(), any(), any())).thenReturn(1); // delete operations
        when(jdbcTemplate.update(anyString(), any(), any(), any())).thenReturn(1); // generate operation
        when(jdbcTemplate.queryForObject(anyString(), eq(Integer.class), any(), any())).thenReturn(1, 3, 2);

        // When
        ProcurementExecutionBasisGenerationService.GenerationResult result = 
            procurementExecutionBasisGenerationService.regenerateFromQuarterlyBudget(testBudgetId, testTenantId, testOperatorBy);

        // Then
        assertTrue(result.isSuccess());
        assertEquals(1, result.getMainRecordCount());
        assertEquals(3, result.getDetailRecordCount());
        assertEquals(2, result.getMaterialCount());

        // Verify delete operations were called
        verify(jdbcTemplate, atLeast(1)).update(contains("DELETE"), any(), any());
        // Verify generate operation was called
        verify(jdbcTemplate).update("CALL sp_generate_procurement_execution_basis(?, ?, ?)", 
                testBudgetId, testTenantId, testOperatorBy);
    }

    @Test
    void testGenerationResult_ToString() {
        // Test success result
        ProcurementExecutionBasisGenerationService.GenerationResult successResult = 
            ProcurementExecutionBasisGenerationService.GenerationResult.success(2, 5, 3, 1000);
        
        String successString = successResult.toString();
        assertTrue(successString.contains("生成成功"));
        assertTrue(successString.contains("主表2条"));
        assertTrue(successString.contains("明细5条"));
        assertTrue(successString.contains("物料3种"));
        assertTrue(successString.contains("耗时1000ms"));

        // Test failure result
        ProcurementExecutionBasisGenerationService.GenerationResult failureResult = 
            ProcurementExecutionBasisGenerationService.GenerationResult.failure("测试错误", 500);
        
        String failureString = failureResult.toString();
        assertTrue(failureString.contains("生成失败"));
        assertTrue(failureString.contains("测试错误"));
        assertTrue(failureString.contains("耗时500ms"));
    }

    @Test
    void testBatchGenerationResult_ToString() {
        // Given
        List<String> failureDetails = Arrays.asList("预算1失败", "预算2失败");
        ProcurementExecutionBasisGenerationService.BatchGenerationResult result = 
            new ProcurementExecutionBasisGenerationService.BatchGenerationResult(
                5, 3, 2, 10, 25, failureDetails, 2000);

        // When
        String resultString = result.toString();

        // Then
        assertTrue(resultString.contains("批量生成完成"));
        assertTrue(resultString.contains("总数5"));
        assertTrue(resultString.contains("成功3"));
        assertTrue(resultString.contains("失败2"));
        assertTrue(resultString.contains("主表10条"));
        assertTrue(resultString.contains("明细25条"));
        assertTrue(resultString.contains("耗时2000ms"));
    }
}
