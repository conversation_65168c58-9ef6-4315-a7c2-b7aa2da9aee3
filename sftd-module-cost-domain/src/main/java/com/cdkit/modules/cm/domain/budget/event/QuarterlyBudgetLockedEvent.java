package com.cdkit.modules.cm.domain.budget.event;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 季度预算锁定领域事件
 * 当季度预算状态变更为LOCKED时发布此事件
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QuarterlyBudgetLockedEvent {

    /**
     * 季度预算ID
     */
    private String budgetId;

    /**
     * 租户ID
     */
    private Integer tenantId;

    /**
     * 季度预算单号
     */
    private String quarterlyBudgetNo;

    /**
     * 季度预算名称
     */
    private String quarterlyBudgetName;

    /**
     * 事件发生时间
     */
    private LocalDateTime eventTime;

    /**
     * 操作人
     */
    private String operatorBy;

    /**
     * 构造函数 - 基本信息
     *
     * @param budgetId 季度预算ID
     * @param tenantId 租户ID
     */
    public QuarterlyBudgetLockedEvent(String budgetId, Integer tenantId) {
        this.budgetId = budgetId;
        this.tenantId = tenantId;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 构造函数 - 完整信息
     *
     * @param budgetId 季度预算ID
     * @param tenantId 租户ID
     * @param quarterlyBudgetNo 季度预算单号
     * @param quarterlyBudgetName 季度预算名称
     * @param operatorBy 操作人
     */
    public QuarterlyBudgetLockedEvent(String budgetId, Integer tenantId, String quarterlyBudgetNo, 
                                     String quarterlyBudgetName, String operatorBy) {
        this.budgetId = budgetId;
        this.tenantId = tenantId;
        this.quarterlyBudgetNo = quarterlyBudgetNo;
        this.quarterlyBudgetName = quarterlyBudgetName;
        this.operatorBy = operatorBy;
        this.eventTime = LocalDateTime.now();
    }

    /**
     * 获取事件描述
     *
     * @return 事件描述信息
     */
    public String getEventDescription() {
        return String.format("季度预算[%s-%s]已锁定，触发采购执行依据生成", 
                quarterlyBudgetNo, quarterlyBudgetName);
    }

    @Override
    public String toString() {
        return "QuarterlyBudgetLockedEvent{" +
                "budgetId='" + budgetId + '\'' +
                ", tenantId=" + tenantId +
                ", quarterlyBudgetNo='" + quarterlyBudgetNo + '\'' +
                ", quarterlyBudgetName='" + quarterlyBudgetName + '\'' +
                ", eventTime=" + eventTime +
                ", operatorBy='" + operatorBy + '\'' +
                '}';
    }
}
