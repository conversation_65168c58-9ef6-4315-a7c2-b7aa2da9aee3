package com.cdkit.modules.cm.domain.procurement.service.impl;

import com.cdkit.modules.cm.domain.procurement.service.ProcurementExecutionBasisGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 采购执行依据生成领域服务实现
 * 
 * <AUTHOR>
 * @date 2025-08-21
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ProcurementExecutionBasisGenerationServiceImpl implements ProcurementExecutionBasisGenerationService {

    private final JdbcTemplate jdbcTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GenerationResult generateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始生成采购执行依据，季度预算ID: {}, 租户ID: {}, 操作人: {}", 
                quarterlyBudgetId, tenantId, operatorBy);

        // 参数校验
        if (!StringUtils.hasText(quarterlyBudgetId)) {
            String errorMsg = "季度预算ID不能为空";
            log.error(errorMsg);
            return GenerationResult.failure(errorMsg, System.currentTimeMillis() - startTime);
        }

        if (tenantId == null) {
            String errorMsg = "租户ID不能为空";
            log.error(errorMsg);
            return GenerationResult.failure(errorMsg, System.currentTimeMillis() - startTime);
        }

        try {
            // 调用存储过程生成采购执行依据
            String sql = "CALL sp_generate_procurement_execution_basis(?, ?, ?)";
            jdbcTemplate.update(sql, quarterlyBudgetId, tenantId, operatorBy);

            // 查询生成结果统计
            GenerationStatistics statistics = queryGenerationStatistics(quarterlyBudgetId, tenantId);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            log.info("生成采购执行依据完成，季度预算ID: {}, 主表记录: {}, 明细记录: {}, 物料数量: {}, 耗时: {}ms", 
                    quarterlyBudgetId, statistics.mainRecordCount, statistics.detailRecordCount, 
                    statistics.materialCount, processingTime);

            return GenerationResult.success(statistics.mainRecordCount, statistics.detailRecordCount, 
                                          statistics.materialCount, processingTime);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMsg = "生成采购执行依据失败: " + e.getMessage();
            log.error("生成采购执行依据失败，季度预算ID: {}", quarterlyBudgetId, e);
            return GenerationResult.failure(errorMsg, processingTime);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BatchGenerationResult batchGenerateFromQuarterlyBudgets(List<String> quarterlyBudgetIds, 
                                                                  Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始批量生成采购执行依据，预算数量: {}, 租户ID: {}, 操作人: {}", 
                quarterlyBudgetIds.size(), tenantId, operatorBy);

        if (quarterlyBudgetIds == null || quarterlyBudgetIds.isEmpty()) {
            String errorMsg = "季度预算ID列表不能为空";
            log.error(errorMsg);
            return new BatchGenerationResult(0, 0, 0, 0, 0, 
                    List.of(errorMsg), System.currentTimeMillis() - startTime);
        }

        int totalCount = quarterlyBudgetIds.size();
        int successCount = 0;
        int failureCount = 0;
        int totalMainRecordCount = 0;
        int totalDetailRecordCount = 0;
        List<String> failureDetails = new ArrayList<>();

        for (String budgetId : quarterlyBudgetIds) {
            try {
                GenerationResult result = generateFromQuarterlyBudget(budgetId, tenantId, operatorBy);
                if (result.isSuccess()) {
                    successCount++;
                    totalMainRecordCount += result.getMainRecordCount();
                    totalDetailRecordCount += result.getDetailRecordCount();
                } else {
                    failureCount++;
                    failureDetails.add(String.format("预算ID[%s]: %s", budgetId, result.getErrorMessage()));
                }
            } catch (Exception e) {
                failureCount++;
                failureDetails.add(String.format("预算ID[%s]: %s", budgetId, e.getMessage()));
                log.error("批量生成采购执行依据失败，预算ID: {}", budgetId, e);
            }
        }

        long totalProcessingTime = System.currentTimeMillis() - startTime;
        
        log.info("批量生成采购执行依据完成，总数: {}, 成功: {}, 失败: {}, 主表: {}, 明细: {}, 耗时: {}ms", 
                totalCount, successCount, failureCount, totalMainRecordCount, totalDetailRecordCount, totalProcessingTime);

        return new BatchGenerationResult(totalCount, successCount, failureCount,
                totalMainRecordCount, totalDetailRecordCount, failureDetails, totalProcessingTime);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public GenerationResult regenerateFromQuarterlyBudget(String quarterlyBudgetId, Integer tenantId, String operatorBy) {
        long startTime = System.currentTimeMillis();
        
        log.info("开始重新生成采购执行依据，季度预算ID: {}, 租户ID: {}, 操作人: {}", 
                quarterlyBudgetId, tenantId, operatorBy);

        try {
            // 先删除现有的采购执行依据数据
            deleteExistingExecutionBasis(quarterlyBudgetId, tenantId);
            
            // 重新生成
            return generateFromQuarterlyBudget(quarterlyBudgetId, tenantId, operatorBy);

        } catch (Exception e) {
            long processingTime = System.currentTimeMillis() - startTime;
            String errorMsg = "重新生成采购执行依据失败: " + e.getMessage();
            log.error("重新生成采购执行依据失败，季度预算ID: {}", quarterlyBudgetId, e);
            return GenerationResult.failure(errorMsg, processingTime);
        }
    }

    /**
     * 查询生成结果统计信息
     */
    private GenerationStatistics queryGenerationStatistics(String quarterlyBudgetId, Integer tenantId) {
        try {
            // 查询主表记录数
            String mainCountSql = """
                SELECT COUNT(DISTINCT peb.id) 
                FROM cost_procurement_execution_basis peb
                INNER JOIN cost_procurement_execution_basis_detail pebd ON peb.id = pebd.execution_basis_id
                WHERE pebd.quarterly_budget_id = ? AND peb.tenant_id = ? AND peb.del_flag = 0
                """;
            Integer mainCount = jdbcTemplate.queryForObject(mainCountSql, Integer.class, quarterlyBudgetId, tenantId);

            // 查询明细记录数
            String detailCountSql = """
                SELECT COUNT(*) 
                FROM cost_procurement_execution_basis_detail 
                WHERE quarterly_budget_id = ? AND tenant_id = ? AND del_flag = 0
                """;
            Integer detailCount = jdbcTemplate.queryForObject(detailCountSql, Integer.class, quarterlyBudgetId, tenantId);

            // 查询物料数量
            String materialCountSql = """
                SELECT COUNT(DISTINCT peb.material_code) 
                FROM cost_procurement_execution_basis peb
                INNER JOIN cost_procurement_execution_basis_detail pebd ON peb.id = pebd.execution_basis_id
                WHERE pebd.quarterly_budget_id = ? AND peb.tenant_id = ? AND peb.del_flag = 0
                """;
            Integer materialCount = jdbcTemplate.queryForObject(materialCountSql, Integer.class, quarterlyBudgetId, tenantId);

            return new GenerationStatistics(
                    mainCount != null ? mainCount : 0,
                    detailCount != null ? detailCount : 0,
                    materialCount != null ? materialCount : 0
            );

        } catch (Exception e) {
            log.warn("查询生成结果统计信息失败，使用默认值", e);
            return new GenerationStatistics(0, 0, 0);
        }
    }

    /**
     * 删除现有的采购执行依据数据
     */
    private void deleteExistingExecutionBasis(String quarterlyBudgetId, Integer tenantId) {
        try {
            // 删除明细表数据
            String deleteDetailSql = """
                DELETE FROM cost_procurement_execution_basis_detail 
                WHERE quarterly_budget_id = ? AND tenant_id = ?
                """;
            int detailDeleteCount = jdbcTemplate.update(deleteDetailSql, quarterlyBudgetId, tenantId);

            // 删除没有明细的主表数据
            String deleteMainSql = """
                DELETE peb FROM cost_procurement_execution_basis peb
                LEFT JOIN cost_procurement_execution_basis_detail pebd ON peb.id = pebd.execution_basis_id
                WHERE pebd.id IS NULL AND peb.tenant_id = ?
                """;
            int mainDeleteCount = jdbcTemplate.update(deleteMainSql, tenantId);

            log.info("删除现有采购执行依据数据完成，明细: {}条, 主表: {}条", detailDeleteCount, mainDeleteCount);

        } catch (Exception e) {
            log.error("删除现有采购执行依据数据失败", e);
            throw new RuntimeException("删除现有数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成统计信息
     */
    private static class GenerationStatistics {
        final int mainRecordCount;
        final int detailRecordCount;
        final int materialCount;

        GenerationStatistics(int mainRecordCount, int detailRecordCount, int materialCount) {
            this.mainRecordCount = mainRecordCount;
            this.detailRecordCount = detailRecordCount;
            this.materialCount = materialCount;
        }
    }
}
