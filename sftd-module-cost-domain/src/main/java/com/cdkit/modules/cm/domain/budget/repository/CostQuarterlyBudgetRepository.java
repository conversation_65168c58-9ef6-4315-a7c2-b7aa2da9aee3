package com.cdkit.modules.cm.domain.budget.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.budget.mode.entity.CostQuarterlyBudgetEntity;

import java.util.List;

/**
 * 季度预算仓储接口
 * 定义季度预算的持久化操作
 * 
 * <AUTHOR>
 * @date 2025-08-12
 */
public interface CostQuarterlyBudgetRepository extends IBaseDomainRepository<CostQuarterlyBudgetEntity, CostQuarterlyBudgetEntity> {

    /**
     * 保存季度预算
     *
     * @param entity 季度预算实体
     * @return 保存结果
     */
    CostQuarterlyBudgetEntity save(CostQuarterlyBudgetEntity entity);

    /**
     * 根据ID更新季度预算
     *
     * @param entity 季度预算实体
     * @return 更新结果
     */
    CostQuarterlyBudgetEntity updateById(CostQuarterlyBudgetEntity entity);

    /**
     * 根据ID删除季度预算
     *
     * @param id 季度预算ID
     */
    void deleteById(String id);

    /**
     * 批量删除季度预算
     *
     * @param ids 季度预算ID列表
     */
    void deleteByIds(List<String> ids);

    /**
     * 根据ID查询季度预算
     *
     * @param id 季度预算ID
     * @return 季度预算实体
     */
    CostQuarterlyBudgetEntity findById(String id);

    /**
     * 根据ID列表查询季度预算
     *
     * @param ids 季度预算ID列表
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByIds(List<String> ids);

    /**
     * 分页查询季度预算
     *
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<CostQuarterlyBudgetEntity> queryPageList(CostQuarterlyBudgetEntity queryEntity, PageReq pageReq);

    /**
     * 根据季度预算单号查询季度预算
     *
     * @param quarterlyBudgetNo 季度预算单号
     * @return 季度预算实体
     */
    CostQuarterlyBudgetEntity findByQuarterlyBudgetNo(String quarterlyBudgetNo);

    /**
     * 根据季度预算编码查询季度预算
     *
     * @param quarterlyBudgetCode 季度预算编码
     * @return 季度预算实体
     */
    CostQuarterlyBudgetEntity findByQuarterlyBudgetCode(String quarterlyBudgetCode);

    /**
     * 根据状态查询季度预算列表
     *
     * @param budgetStatus 预算状态
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByBudgetStatus(String budgetStatus);

    /**
     * 根据ID列表查询季度预算列表
     *
     * @param budgetIds 预算ID列表
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByIds(List<String> budgetIds);

    /**
     * 根据日期查询最大的季度预算单号
     * 用于生成下一个季度预算编号
     *
     * @param date 日期（8位格式，如：20250812）
     * @return 最大的季度预算单号，如果没有记录则返回null
     */
    String findMaxQuarterlyBudgetNoByDate(String date);

    /**
     * 根据年度预算编码查询关联的季度预算列表
     *
     * @param annualBudgetCode 年度预算编码
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByAnnualBudgetId(String annualBudgetCode);

    /**
     * 根据季度计划编号查询关联的季度预算列表
     *
     * @param quarterlyPlanNo 季度计划编号
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByQuarterlyPlanId(String quarterlyPlanNo);

    /**
     * 根据年度预算编码和状态查询季度预算列表
     *
     * @param annualBudgetCode 年度预算编码
     * @param budgetStatus 预算状态
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByAnnualBudgetIdAndStatus(String annualBudgetCode, String budgetStatus);

    /**
     * 根据所属单位查询季度预算列表
     *
     * @param professionalCompany 所属单位
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByProfessionalCompany(String professionalCompany);

    /**
     * 根据季度查询季度预算列表
     *
     * @param quarter 季度（如：2025Q1）
     * @return 季度预算实体列表
     */
    List<CostQuarterlyBudgetEntity> findByQuarter(String quarter);

    /**
     * 批量更新季度预算状态
     *
     * @param budgetIds 预算ID列表
     * @param newStatus 新状态
     */
    void updateBudgetStatusBatch(List<String> budgetIds, String newStatus);

    /**
     * 汇总与指定年度预算相关的已审批通过的季度预算的项目预算总额
     * 用于计算年度收入剩余预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的项目预算总额
     */
    java.math.BigDecimal sumApprovedQuarterlyBudgetRevenueByAnnualBudgetId(String annualBudgetId, String excludeQuarterlyBudgetId);

    /**
     * 汇总与指定年度预算和预算科目相关的已审批通过的季度预算的支出金额
     * 用于计算年度剩余支出预算金额
     *
     * @param annualBudgetId 年度预算ID
     * @param budgetSubjectCode 预算科目编码
     * @param excludeQuarterlyBudgetId 排除的季度预算ID（可为null）
     * @return 已审批季度预算的支出金额
     */
    java.math.BigDecimal sumApprovedQuarterlyBudgetExpenditureBySubjectCode(String annualBudgetId, String budgetSubjectCode, String excludeQuarterlyBudgetId);
}
